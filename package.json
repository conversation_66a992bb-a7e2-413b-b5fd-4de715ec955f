{"name": "n8n-nodes-emailconnect", "version": "0.1.4", "description": "n8n community node for EmailConnect - Email automation and webhook integration", "keywords": ["n8n-community-node-package", "n8n", "emailconnect", "email", "automation", "webhook", "workflow", "integration"], "license": "MIT", "homepage": "https://github.com/xadi-hq/n8n-nodes-emailconnect#readme", "author": {"name": "<PERSON>ander", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/xadi-hq/n8n-nodes-emailconnect.git"}, "bugs": {"url": "https://github.com/xadi-hq/n8n-nodes-emailconnect/issues"}, "engines": {"node": ">=20.0.0", "npm": ">=8.0.0"}, "main": "index.js", "scripts": {"build": "tsc && gulp build:icons", "dev": "tsc --watch", "format": "prettier nodes credentials --write", "lint": "eslint nodes credentials", "lintfix": "eslint nodes credentials --fix", "test": "jest", "test:watch": "jest --watch", "prepublishOnly": "npm run build && npm run lint -s"}, "files": ["dist"], "n8n": {"n8nNodesApiVersion": 1, "credentials": ["dist/credentials/EmailConnectApi.credentials.js"], "nodes": ["dist/nodes/EmailConnect/EmailConnect.node.js", "dist/nodes/EmailConnectTrigger/EmailConnectTrigger.node.js"]}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.29.0", "@typescript-eslint/parser": "^8.34.1", "eslint": "^9.29.0", "eslint-plugin-n8n-nodes-base": "^1.16.1", "globals": "^16.2.0", "gulp": "^5.0.1", "jest": "^30.0.2", "n8n-workflow": "^1.2.0", "prettier": "^3.3.2", "typescript": "^5.5.3"}, "peerDependencies": {"n8n-workflow": "*"}}